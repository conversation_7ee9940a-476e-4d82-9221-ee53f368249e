<?php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "db_sampah";

$conn = mysqli_connect($servername, $username, $password, $dbname);

if (isset($_POST['driver_id']) && isset($_POST['user_id']) && isset($_POST['nopol']) && isset($_POST['tipe_kendaraan'])) {
    $driver_id = $_POST['driver_id'];
    $user_id = $_POST['user_id'];
    $nopol = $_POST['nopol'];
    $tipe_kendaraan = $_POST['tipe_kendaraan'];

    $driver_id = mysqli_real_escape_string($conn, $driver_id);
    $user_id = mysqli_real_escape_string($conn, $user_id);
    $nopol = mysqli_real_escape_string($conn, $nopol);
    $tipe_kendaraan = mysqli_real_escape_string($conn, $tipe_kendaraan);

    $sql = "INSERT INTO driver (driver_id, user_id, nopol, tipe_kendaraan)
    VALUES ('$driver_id', '$user_id', '$nopol', '$tipe_kendaraan')";

    if (mysqli_query($conn, $sql)) {
        echo "New record created successfully";
        header('location: index.php');
        exit;
    } else {
        echo "Error: " . $sql . "<br>" . mysqli_error($conn);
    }
}

mysqli_close($conn);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Add Kendaraan Modal</title>
    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 50%;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h2>Add Kendaraan</h2>


    <script>
        // Get the modal element
        var modal = document.getElementById("addKendaraanModal");

        // Function to open the modal
        function openModal() {
            modal.style.display = "block";
        }

        // Function to close the modal
        function closeModal() {
            modal.style.display = "none";
        }

        // Close the modal when the user clicks outside of it
        window.onclick = function(event) {
            if (event.target == modal) {
                closeModal();
            }
        };
    </script>
</body>
</html>