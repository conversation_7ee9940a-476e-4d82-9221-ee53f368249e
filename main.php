<?php
session_start();

if (isset($_SESSION['user_id'])) {

} else {
    header('Location: index.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat+Alternates&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unicons.iconscout.com/release/v4.0.0/css/line.css">
    <script src="https://www.gstatic.com/charts/loader.js"></script>
    <link rel="manifest" href="/manifest.json">
    <script src="https://cdn.tailwindcss.com"></script>
    <title>Beranda</title>
    <style>
        * {
            font-family: 'Montserrat Alternates', sans-serif;
        }

        body {
            min-height: 100vh;
            min-height: -webkit-fill-available;
        }

        html {
            height: -webkit-fill-available;
        }

        .slide {
            animation-name: slide;
            animation-duration: 0.8s;
        }

        @keyframes slide {
            from {
                width: 0%;
            }

            to {
                width: 100%;
            }
        }

        .slides {
            animation-name: slides;
            animation-duration: 1s;
        }

        @keyframes slides {
            from {
                width: 40px;
            }

            to {
                width: 100%;
            }
        }

        #scroll2 {
            background-image: url('./assets/images/bgScroll2.svg');
            background-repeat: no-repeat;
            background-size: cover;
        }
    </style>
</head>

<body class="w-screen h-screen">
<div id="location-warning" style="display: none"
        class="w-full h-full fixed top-0 left-0 z-[999] bg-black/50 flex px-6 items-center justify-center">
        <div class="bg-white rounded-xl p-6 w-full text-center max-w-md">
            <p>Aplikasi ini memerlukan akses lokasi agar dapat berfungsi dengan baik. Pastikan layanan lokasi di perangkat Anda aktif dan izinkan aplikasi ini untuk mengakses lokasi Anda. Jika Anda telah menolak izin, klik tautan <a href="izin_lokasi.php" class="underline text-[#7DDD98]" target="_blank">Izin Lokasi</a> dan ikuti petunjuk di halaman tersebut untuk memberikan izin akses lokasi.</p>
        </div>
    </div>
    <div id="main" class="fixed bg-white h-full w-full">
        <div id="menuContent" class="h-[93%] bg-white rounded-b-3xl"><?php include 'beranda.php';
?>
           <?php
include 'config.php';

if (isset($_SESSION['user_id'])) {
    $data = array();
    $query_data = "SELECT MONTH(report_date) AS bulan, COUNT(*) AS jumlah_laporan FROM report WHERE user_id = '$user_id' GROUP BY MONTH(report_date)";
    $result_data = mysqli_query($conn, $query_data);

    while ($row = mysqli_fetch_assoc($result_data)) {
        $bulan = $row['bulan'];
        $jumlah_laporan = $row['jumlah_laporan'];
        $data[] = array($bulan, $jumlah_laporan);
    }
} else {
    header("Location: login.php");
    exit();
}
?>
</div>
        <div id="menuContent" class="h-[93%] bg-white overflow-y-auto pb-2 hidden rounded-b-3xl"><?php include 'riwayat.php';
?></div>
        <div id="menuContent" class="h-[93%] bg-white hidden rounded-b-3xl">
            <?php include 'akun.php';?>
        </div>
        <div class="h-[7%] bg-[#7DDD98] flex px-2 z-[999] rounded-t-3xl">
            <div class="w-4/12 flex items-center">
                <div id="menu" class="bg-white px-2 py-1 duration-300 rounded-xl max-w-max flex items-center m-auto">
                    <img class="" src="./assets/icons/estate.svg" alt="estate.svg"><span id="menuBtn"
                        class="text-sm ml-1 text-[#7DDD98] font-bold">Beranda</span></div>
            </div>
            <div class="w-4/12 flex items-center">
                <div id="menu" class="bg-transparent px-2 py-1 rounded-xl max-w-max flex items-center m-auto"><img
                        class="" src="./assets/icons/historyWhite.svg" alt="estate.svg"><span id="menuBtn"
                        class="text-sm ml-1 text-[#7DDD98] font-bold hidden">Riwayat</span></div>
            </div>
            <div class="w-4/12 flex items-center">
                <div id="menu" class="bg-transparent px-2 py-1 rounded-xl max-w-max flex items-center m-auto"><img
                        class="" src="./assets/icons/settingWhite.svg" alt="estate.svg"><span id="menuBtn"
                        class="text-sm ml-1 text-[#7DDD98] font-bold hidden">Akun</span></div>
            </div>
        </div>
    </div>

    <div id="modal-content" class="hidden">
        <span class="close">&times;</span>
        <p>Anda tidak memiliki akses untuk melihat laporan.</p>
        <button id="subscribeBtn">Berlangganan</button>
        <button id="cancelBtn">Batal</button>
    </div>

</body>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="fullscreen.js"></script>
<script>
    // document.addEventListener('DOMContentLoaded', cekLocation); // Disabled auto-check
    
    const menu = document.querySelectorAll("#menu");
    const menuContent = document.querySelectorAll("#menuContent");
    const menuBtn = document.querySelectorAll("#menuBtn");
    const img = document.querySelectorAll("#menu img");
    reports = document.querySelectorAll('#report');
    details = document.querySelectorAll('#detail');
    checks = document.querySelectorAll('#checkDetail');

    function showMenu(menuIndex, btnIndex, contentIndex, img1, img2, img3) {
        menuBtn.forEach((btn) => btn.classList.remove("slide"));
        menu.forEach((item) => item.classList.remove("slides"));
        menu.forEach((item) => item.classList.add("bg-transparent"));
        menu.forEach((item) => item.classList.remove("bg-white"));
        menu[menuIndex].classList.remove("bg-transparent");
        menu[menuIndex].classList.add("bg-white");
        menu[menuIndex].classList.add("slides");
        menuBtn[btnIndex].classList.add("slide");
        menuContent.forEach((content) => content.classList.add("hidden"));
        menuContent[contentIndex].classList.remove("hidden");
        menuBtn.forEach((btn) => btn.classList.add("hidden"));
        menuBtn[btnIndex].classList.remove("hidden");
        img[0].src = img1;
        img[1].src = img2;
        img[2].src = img3;

        if (menuIndex === 0 || menuIndex === 2) {
            checks.forEach((check, index) => {
                const detail = details[index];
                detail.classList.remove('detailr');
                detail.classList.remove('details');
                reports[index].classList.remove('reportr');
                reports[index].classList.remove('reports');
            });
        }
    }

    menu[0].addEventListener("click", () => {
            showMenu(0, 0, 0, "./assets/icons/estate.svg", "./assets/icons/historyWhite.svg",
                "./assets/icons/settingWhite.svg");
        }

    );

    menu[1].addEventListener("click", () => {
            showMenu(1, 1, 1, "./assets/icons/estateWhite.svg", "./assets/icons/history.svg",
                "./assets/icons/settingWhite.svg");
        }

    );

    menu[2].addEventListener("click", () => {
            showMenu(2, 2, 2, "./assets/icons/estateWhite.svg", "./assets/icons/historyWhite.svg",
                "./assets/icons/setting.svg");
        }

    );


    maps = document.querySelector("#maps");

    maps.addEventListener('click', () => {
            window.location.href = "map.php";
        }

    );
    
    mapss = document.querySelector("#mapss");

    mapss.addEventListener('click', () => {
            window.location.href = "map.php";
        }

    );

    editAkun = document.querySelector("#editAkun");

    editAkun.addEventListener('click', () => {
            window.location.href = "editAkun.php";
        }

    );

    editsAkun = document.querySelector("#editsAkun");

    editsAkun.addEventListener('click', () => {
            window.location.href = "editAkun.php";
        }
    );

    sampah = document.querySelector("#sampah");

    sampah.addEventListener('click', () => {
            checkUserStatus();
        }
    );

    function checkUserStatus() {
    // Ambil user_id dari session
    let user_id = <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'null'; ?>;
    
    if (!user_id) {
        console.error('User ID tidak tersedia.');
        return;
    }

    // Fungsi untuk mengirim request Ajax dengan latitude dan longitude
    function sendAjaxRequest(latitude, longitude) {
        let xhr = new XMLHttpRequest();
        xhr.open('GET', 'check_status.php?user_id=' + user_id + '&latitude=' + latitude + '&longitude=' + longitude, true);
        
        xhr.onload = function() {
            if (xhr.status == 200) {
                let response = xhr.responseText.trim();
                console.log(response);

                try {
                    let data = JSON.parse(response);
                    let status = data.status;
                    let areaId = data.area_id;

                    console.log('Area ID: ' + areaId);
                    
                    if (status === '1') {
                        // Jika status adalah 1, lanjutkan ke laporan.php
                        window.location.href = "alamat_laporan.php";
                    } else if (status === '0') {
                        // Jika status adalah 0, tampilkan alert
                        window.location.href = "langganan.php?area_id=" + areaId;
                    } else {
                        // Handle kondisi lainnya jika diperlukan
                        console.log('Status tidak valid: ' + status);
                    }
                } catch (e) {
                    if (response === 'invalid') {
                        window.location.href = "area.php";
                    } else {
                        console.error('Response tidak valid: ' + response);
                    }
                }
            } else {
                // Handle kesalahan jika request tidak berhasil
                console.error('Request tidak berhasil. Status: ' + xhr.status);
            }
        };
        
        xhr.onerror = function() {
            // Handle kesalahan ketika terjadi error
            console.error('Terjadi error pada request.');
        };
        
        xhr.send();
    }

    // Dapatkan lokasi pengguna
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                // Jika lokasi berhasil diperoleh
                let latitude = position.coords.latitude;
                let longitude = position.coords.longitude;
                sendAjaxRequest(latitude, longitude);  // Ganti dengan fungsi AJAX Anda
            }, 
            function(error) {
                if (error.code === error.PERMISSION_DENIED) {
                    // Tampilkan peringatan jika pengguna menolak akses lokasi
                    document.getElementById('location-warning').style.display = 'flex';
                    document.getElementById('retry').style.display = 'inline';
                } else {
                    document.getElementById('location-warning').style.display = 'none';
                    document.getElementById('retry').style.display = 'inline';
                }
            }
        );
    } else {
        console.error('Geolocation tidak didukung oleh browser ini.');
    }
}

// cekLocation(); // Disabled auto-check

function cekLocation() {
    // Ambil user_id dari session
    let user_id = <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'null'; ?>;
    
    if (!user_id) {
        console.error('User ID tidak tersedia.');
        return;
    }

    // Fungsi untuk mengirim request Ajax dengan latitude dan longitude
    function sendAjaxRequest(latitude, longitude) {
        let xhr = new XMLHttpRequest();
        xhr.open('GET', 'check_status.php?user_id=' + user_id + '&latitude=' + latitude + '&longitude=' + longitude, true);
        
        xhr.onload = function() {
            if (xhr.status == 200) {
                let response = xhr.responseText.trim();
                console.log(response);

                try {
                    let data = JSON.parse(response);
                    let status = data.status;
                    let areaId = data.area_id;

                    console.log('Area ID: ' + areaId);
                    
                    if (status === '1') {
                    } else if (status === '0') {
                        // Jika status adalah 0, tampilkan alert
                    } else {
                        // Handle kondisi lainnya jika diperlukan
                        console.log('Status tidak valid: ' + status);
                    }
                } catch (e) {
                    if (response === 'invalid') {
                        window.location.href = "area.php";
                    } else {
                        console.error('Response tidak valid: ' + response);
                    }
                }
            } else {
                // Handle kesalahan jika request tidak berhasil
                console.error('Request tidak berhasil. Status: ' + xhr.status);
            }
        };
        
        xhr.onerror = function() {
            // Handle kesalahan ketika terjadi error
            console.error('Terjadi error pada request.');
        };
        
        xhr.send();
    }

    // Dapatkan lokasi pengguna
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                // Jika lokasi berhasil diperoleh
                let latitude = position.coords.latitude;
                let longitude = position.coords.longitude;
                sendAjaxRequest(latitude, longitude);  // Ganti dengan fungsi AJAX Anda
            }, 
            function(error) {
                if (error.code === error.PERMISSION_DENIED) {
                    // Tampilkan peringatan jika pengguna menolak akses lokasi
                    document.getElementById('location-warning').style.display = 'flex';
                    document.getElementById('retry').style.display = 'inline';
                } else {
                    document.getElementById('location-warning').style.display = 'none';
                    document.getElementById('retry').style.display = 'inline';
                }
            }
        );
    } else {
        console.error('Geolocation tidak didukung oleh browser ini.');
    }
}

function openLocationSettings() {
        // Memeriksa jenis browser dan membuka pengaturan yang sesuai
        if (navigator.userAgent.includes("Chrome")) {
            window.open('chrome://settings/content/location', '_blank');
        } else if (navigator.userAgent.includes("Firefox")) {
            window.open('about:preferences#privacy', '_blank');
        } else if (navigator.userAgent.includes("Edge")) {
            window.open('edge://settings/content/location', '_blank');
        } else {
            alert("Silakan buka pengaturan browser Anda secara manual untuk mengubah izin lokasi.");
        }
    }

let cancelBtn = document.getElementById('cancelBtn');
cancelBtn.addEventListener('click', function() {
                // Tutup modal
                document.body.removeChild(modal);
            });

    
    sampahs = document.querySelector("#sampahs");

    sampahs.addEventListener('click', () => {
            checkUserStatus();
        }

    );

    document.querySelector('#tentang').addEventListener('click', () => {
            window.location.href = "tentangAplikasi.php";
        }

    );

    document.querySelector('#keluar').addEventListener('click', () => {
            window.location.href = "keluar.php";
        }

    );

        btnBerlangsung = document.querySelector('#btnBerlangsung');
        btnSelesai = document.querySelector('#btnSelesai');
        berlangsung = document.querySelector('#berlangsung');
        selesai = document.querySelector('#selesai');
        animBtn1 = document.querySelector('#animbtn1');
        animBtn2 = document.querySelector('#animbtn2');

        var lastActive = 'berlangsung';
        if (selesai.classList.contains('hidden')) {
            lastActive = 'selesai';
        }

        var btnBerlangsungClicked = false;

        btnBerlangsung.addEventListener('click', () => {
            berlangsung.classList.remove('hidden');
            selesai.classList.add('hidden');
            animBtn1.classList.add('lebar');
            animBtn2.classList.remove('lebar');
            animBtn1.classList.remove('w-0');
            animBtn2.classList.add('w-0');
            checks.forEach((check, index) => {
                const detail = details[index];
                detail.classList.remove('detailr');
                detail.classList.remove('details');
                reports[index].classList.remove('reportr');
                reports[index].classList.remove('reports');
            });
            lastActive = 'berlangsung';
            btnBerlangsungClicked = true;
        });

        btnSelesai.addEventListener('click', () => {
            selesai.classList.remove('hidden');
            berlangsung.classList.add('hidden');
            animBtn1.classList.remove('lebar');
            animBtn1.classList.add('w-0');
            animBtn2.classList.add('lebar');
            animBtn2.classList.remove('w-0');
            checks.forEach((check, index) => {
                const detail = details[index];
                detail.classList.remove('detailr');
                detail.classList.remove('details');
                reports[index].classList.remove('reportr');
                reports[index].classList.remove('reports');
            });
            lastActive = 'selesai';
        });

        short = document.querySelector('#short');
        shortMenu = document.querySelector('#shortMenu');
        shorts = document.querySelector('#shorts');

        short.addEventListener('click', () => {
            shorts.checked = !shorts.checked;
            if (shorts.checked) {
                shortMenu.classList.remove('hidden');
            } else {
                shortMenu.classList.add('hidden');
            }
        });

        shorts.addEventListener('click', () => {
            if (shorts.checked) {
                shortMenu.classList.remove('hidden');
            } else {
                shortMenu.classList.add('hidden');
            }
        });


        reports = document.querySelectorAll('#report');
        details = document.querySelectorAll('#detail');
        checks = document.querySelectorAll('#checkDetail');

        reports.forEach((report, index) => {
            report.addEventListener('click', () => {
                checks[index].checked = !checks[index].checked;
                if (checks[index].checked) {
                    const detail = details[index];
                    detail.classList.remove('hidden');
                    detail.classList.add('details');
                    detail.classList.remove('detailr');
                    detail.classList.remove('h-0');
                    detail.classList.add('p-2');
                    detail.classList.remove('p-0');
                    detail.classList.add('h-max');
                    report.classList.add('reports');
                    report.classList.remove('reportr');
                } else {
                    const detail = details[index];
                    setTimeout(() => {
                        detail.classList.add('hidden');
                    }, 1000);
                    detail.classList.remove('details');
                    detail.classList.add('detailr');
                    detail.classList.add('h-0');
                    detail.classList.remove('h-max');
                    report.classList.remove('reports');
                    report.classList.add('reportr');
                }
            });
        });

        const hpsRiwayat = document.querySelectorAll('#hpsRiwayat');
        const checkRiwayat = document.querySelectorAll('#checkRiwayat');
        const tool = document.querySelectorAll('#tool');

        hpsRiwayat.forEach((hps, index) => {
            hps.addEventListener('click', () => {
                checkRiwayat[index].checked = !checkRiwayat[index].checked;
                if (checkRiwayat[index].checked) {
                    tool[index].classList.remove('hidden');
                } else {
                    tool[index].classList.add('hidden');
                }
            });
        });

        var radioButtons = document.querySelectorAll('input[name="urut"]');
        for (var i = 0; i < radioButtons.length; i++) {
            radioButtons[i].addEventListener("change", function () {
                var sortDirection = this.value;

                var xhr = new XMLHttpRequest();
                xhr.onreadystatechange = function () {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        document.getElementById("riwayat").innerHTML = xhr.responseText;
                        console.log("Response Text: " + xhr.responseText);

                        updateEventListeners();

                        if (lastActive === 'berlangsung') {
                            btnBerlangsung.click();
                        } else {
                            btnSelesai.click();
                        }
                    }
                };
                xhr.open("GET", "riwayatMenu.php?sortDirection=" + sortDirection, true);
                xhr.send();
            });
        }

        function updateEventListeners() {
            btnBerlangsung = document.querySelector('#btnBerlangsung');
            btnSelesai = document.querySelector('#btnSelesai');
            berlangsung = document.querySelector('#berlangsung');
            selesai = document.querySelector('#selesai');

            short = document.querySelector('#short');
            shortMenu = document.querySelector('#shortMenu');
            shorts = document.querySelector('#shorts');

            btnBerlangsung.addEventListener('click', () => {
                berlangsung.classList.remove('hidden');
                selesai.classList.add('hidden');
                animBtn1.classList.add('lebar');
                animBtn2.classList.remove('lebar');
                animBtn1.classList.remove('w-0');
                animBtn2.classList.add('w-0');
                checks.forEach((check, index) => {
                const detail = details[index];
                detail.classList.remove('detailr');
                detail.classList.remove('details');
                reports[index].classList.remove('reportr');
                reports[index].classList.remove('reports');
            });
                lastActive = 'berlangsung'; 
                btnBerlangsungClicked = true; 
            });

            btnSelesai.addEventListener('click', () => {
                selesai.classList.remove('hidden');
                berlangsung.classList.add('hidden');
                animBtn1.classList.remove('lebar');
                animBtn1.classList.add('w-0');
                animBtn2.classList.add('lebar');
                animBtn2.classList.remove('w-0');
                checks.forEach((check, index) => {
                const detail = details[index];
                detail.classList.remove('detailr');
                detail.classList.remove('details');
                reports[index].classList.remove('reportr');
                reports[index].classList.remove('reports');
            });
                lastActive = 'selesai';
                btnBerlangsungClicked = false;
            });

            reports = document.querySelectorAll('#report');
            details = document.querySelectorAll('#detail');
            checks = document.querySelectorAll('#checkDetail');

            reports.forEach((report, index) => {
                report.addEventListener('click', () => {
                    checks[index].checked = !checks[index].checked;
                    if (checks[index].checked) {
                        const detail = details[index];
                        detail.classList.remove('hidden');
                        detail.classList.add('details');
                        detail.classList.remove('detailr');
                        detail.classList.remove('h-0');
                        detail.classList.add('p-2');
                        detail.classList.remove('p-0');
                        detail.classList.add('h-max');
                        report.classList.add('reports');
                        report.classList.remove('reportr');
                    } else {
                        const detail = details[index];
                        setTimeout(() => {
                            detail.classList.add('hidden');
                        }, 1000);
                        detail.classList.remove('details');
                        detail.classList.add('detailr');
                        detail.classList.add('h-0');
                        detail.classList.remove('h-max');
                        report.classList.remove('reports');
                        report.classList.add('reportr');
                    }
                });
            });

            const hpsRiwayat = document.querySelectorAll('#hpsRiwayat');
            const checkRiwayat = document.querySelectorAll('#checkRiwayat');
            const tool = document.querySelectorAll('#tool');

            hpsRiwayat.forEach((hps, index) => {
                hps.addEventListener('click', () => {
                    checkRiwayat[index].checked = !checkRiwayat[index].checked;
                    if (checkRiwayat[index].checked) {
                        tool[index].classList.remove('hidden');
                    } else {
                        tool[index].classList.add('hidden');
                    }
                });
            });
        };

        
        const sideBar = document.querySelector('#sideBar');
        const sideName = document.querySelectorAll('#sideName');
        const sideItem = document.querySelectorAll('#sideItem');
        const sideContent = document.querySelectorAll('#sideContent');
        const contentPc = document.querySelectorAll('#contentPc');
        const imgPc = document.querySelectorAll('#imgPc');

        sideBar.addEventListener('click', () => {
            sideBar.classList.remove('w-[5%]');
            sideBar.classList.add('w-[15%]');
            for (let i = 0; i < sideName.length; i++) {
                sideName[i].classList.remove('hidden');
                sideItem[i].classList.remove('m-auto');
            }
        });

        contentPc[0].addEventListener('click', () => {
            sideBar.classList.remove('w-[15%]');
            sideBar.classList.add('w-[5%]');
            setTimeout(() => {
                for (let i = 0; i < sideName.length; i++) {
                    sideName[i].classList.add('hidden');
                    sideItem[i].classList.add('m-auto');
                }
            }, 265);
        });
        contentPc[1].addEventListener('click', () => {
            sideBar.classList.remove('w-[15%]');
            sideBar.classList.add('w-[5%]');
            setTimeout(() => {
                for (let i = 0; i < sideName.length; i++) {
                    sideName[i].classList.add('hidden');
                    sideItem[i].classList.add('m-auto');
                }
            }, 265);
        });
        contentPc[2].addEventListener('click', () => {
            sideBar.classList.remove('w-[15%]');
            sideBar.classList.add('w-[5%]');
            setTimeout(() => {
                for (let i = 0; i < sideName.length; i++) {
                    sideName[i].classList.add('hidden');
                    sideItem[i].classList.add('m-auto');
                }
            }, 265);
        });

        sideContent.forEach((side, index) => {
            const content = contentPc[index];
            const img = imgPc[index];
            side.addEventListener('click', () => {
                contentPc.forEach(c => c.classList.add('hidden'));
                content.classList.remove('hidden');
                sideContent.forEach((s, i) => {
                    s.classList.remove('bg-[#7ddd98ff]', 'text-white', 'text-[#7ddd98ff]');
                    if (i !== index) {
                        s.classList.add('text-[#7ddd98ff]');
                    }
                });
                side.classList.add('bg-[#7ddd98ff]', 'text-white');
                imgPc.forEach(i => i.src = i.src.replace('White', ''));
                img.src = img.src.replace('.svg', 'White.svg');
            });
        });

        function showModal() {
    const modal = document.getElementById('modal');
    modal.classList.remove('hidden');
  }

  function hideModal() {
    const modal = document.getElementById('modal');
    modal.classList.add('hidden');
  }
</script>

<script type="text/javascript">
    google.charts.load('current', {
        'packages': ['corechart']
    });

    google.charts.setOnLoadCallback(drawChart);

    function drawChart() {
        <?php
        if (!empty($data)) {
            ?>
            var data = google.visualization.arrayToDataTable([
                ['Bulan', 'Laporan '],
                <?php
                foreach ($data as $row) {
                    echo "['Bulan " . $row[0] . "', " . $row[1] . "],";
                }
                ?>
            ]);
            <?php
        } else {
            ?>
            var data = google.visualization.arrayToDataTable([
                ['Bulan', 'Laporan '],
                ['Laporan', 0]
            ]);
            <?php
        }
        ?>

        var options = {
            title: 'Statistika Laporan',
            titleTextStyle: {
                color: '#7DDD98'
            },
            hAxis: {
                titleTextStyle: {
                    color: '#7DDD98'
                }
            },
            vAxis: {
                minValue: 0
            },
            colors: ['#7DDD98'],
            legend: {
                position: 'none'
            },
            chartArea: {
                width: '80%',
            },
            pointSize: 5
        };

        var chart = new google.visualization.AreaChart(document.getElementById('chart_div'));
        chart.draw(data, options);
    }
</script>

</html>