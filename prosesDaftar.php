<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'config.php';

// Debug: Check if POST data is received
if (empty($_POST)) {
    die("Error: No POST data received");
}

// Debug: Check required fields
$required_fields = ['username', 'name', 'phone', 'email', 'password', 'level'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        die("Error: Missing required field: $field");
    }
}

$username = $_POST['username'];
$name = $_POST['name'];
$phone = $_POST['phone'];
$email = $_POST['email'];
$password = md5($_POST['password']);
$level = $_POST['level'];
$status = 1; // Set status aktif langsung tanpa verifikasi OTP
$default_image = "./assets/imagesAcc/default.png";

// Debug: Print received data
echo "Debug - Received data:<br>";
echo "Username: $username<br>";
echo "Name: $name<br>";
echo "Phone: $phone<br>";
echo "Email: $email<br>";
echo "Level: $level<br>";
echo "<br>";

$username = mysqli_real_escape_string($conn, $username);
$name = mysqli_real_escape_string($conn, $name);
$phone = mysqli_real_escape_string($conn, $phone);
$email = mysqli_real_escape_string($conn, $email);
$password = mysqli_real_escape_string($conn, $password);
$level = mysqli_real_escape_string($conn, $level);

// Cek apakah email atau nomor HP sudah terdaftar
$query = "SELECT * FROM users WHERE email = '$email' OR no_hp = '$phone'";
echo "Debug - Check duplicate query: $query<br>";
$result = mysqli_query($conn, $query);

if (!$result) {
    die("Error in duplicate check query: " . mysqli_error($conn));
}

if (mysqli_num_rows($result) > 0) {
    // Jika email atau nomor HP sudah terdaftar, munculkan toast
    echo "Debug - Email or phone already exists<br>";
    session_start();
    $_SESSION['error'] = 'Email atau nomor HP sudah terdaftar.';
    header('Location: login.php');
    exit();
} else {
    // Langsung simpan data ke database tanpa OTP
    $sql = "INSERT INTO users (username, full_name, email, no_hp, password, level, image, status)
            VALUES ('$username', '$name', '$email', '$phone', '$password', '$level', '$default_image', '$status')";

    echo "Debug - Insert query: $sql<br>";

    if (mysqli_query($conn, $sql)) {
        echo "Debug - Insert successful! User ID: " . mysqli_insert_id($conn) . "<br>";
        // Registrasi berhasil, redirect ke halaman login dengan pesan sukses
        session_start();
        $_SESSION['success'] = 'Registrasi berhasil! Silakan login dengan akun Anda.';
        echo "Debug - Redirecting to login.php<br>";
        // Temporary disable redirect for debugging
        // header('Location: login.php');
        // exit();
    } else {
        // Error saat menyimpan data
        echo "Debug - Insert failed: " . mysqli_error($conn) . "<br>";
        session_start();
        $_SESSION['error'] = 'Terjadi kesalahan saat mendaftar: ' . mysqli_error($conn);
        header('Location: login.php');
        exit();
    }
}

mysqli_close($conn);
?>
