<?php
include 'config.php';

echo "<h2>Database Connection Test</h2>";

// Test connection
if ($conn) {
    echo "✅ Database connection successful<br><br>";
} else {
    echo "❌ Database connection failed: " . mysqli_connect_error() . "<br>";
    exit;
}

// Check if database exists
$db_check = mysqli_select_db($conn, 'db_sampah');
if ($db_check) {
    echo "✅ Database 'db_sampah' exists<br><br>";
} else {
    echo "❌ Database 'db_sampah' does not exist<br>";
    exit;
}

// Check if users table exists
$table_check = mysqli_query($conn, "SHOW TABLES LIKE 'users'");
if (mysqli_num_rows($table_check) > 0) {
    echo "✅ Table 'users' exists<br><br>";
} else {
    echo "❌ Table 'users' does not exist<br>";
    echo "<h3>Creating users table...</h3>";
    
    $create_table = "CREATE TABLE users (
        user_id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE,
        full_name VARCHA<PERSON>(100),
        email VARCHAR(100) UNIQUE,
        no_hp VARCHAR(20),
        password VARCHAR(255),
        level ENUM('user', 'admin', 'driver') DEFAULT 'user',
        image VARCHAR(255),
        status INT DEFAULT 1,
        join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        tanggal_awal VARCHAR(50),
        tanggal_akhir VARCHAR(50),
        pesan_terkirim INT DEFAULT 0,
        alamat TEXT,
        catatan TEXT,
        longitude DECIMAL(10, 8),
        latitude DECIMAL(11, 8),
        waktu_ambil VARCHAR(20),
        link TEXT
    )";
    
    if (mysqli_query($conn, $create_table)) {
        echo "✅ Table 'users' created successfully<br><br>";
    } else {
        echo "❌ Error creating table: " . mysqli_error($conn) . "<br>";
        exit;
    }
}

// Show table structure
echo "<h3>Table Structure:</h3>";
$structure = mysqli_query($conn, "DESCRIBE users");
if ($structure) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = mysqli_fetch_assoc($structure)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
} else {
    echo "❌ Error getting table structure: " . mysqli_error($conn) . "<br>";
}

// Count existing users
$count_query = mysqli_query($conn, "SELECT COUNT(*) as total FROM users");
if ($count_query) {
    $count = mysqli_fetch_assoc($count_query);
    echo "<h3>Current Users Count: " . $count['total'] . "</h3>";
} else {
    echo "❌ Error counting users: " . mysqli_error($conn) . "<br>";
}

// Show recent users
echo "<h3>Recent Users (Last 5):</h3>";
$recent_users = mysqli_query($conn, "SELECT user_id, username, full_name, email, level, status, join_date FROM users ORDER BY user_id DESC LIMIT 5");
if ($recent_users && mysqli_num_rows($recent_users) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Email</th><th>Level</th><th>Status</th><th>Join Date</th></tr>";
    while ($row = mysqli_fetch_assoc($recent_users)) {
        echo "<tr>";
        echo "<td>" . $row['user_id'] . "</td>";
        echo "<td>" . $row['username'] . "</td>";
        echo "<td>" . $row['full_name'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['level'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['join_date'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No users found or error: " . mysqli_error($conn) . "<br>";
}

mysqli_close($conn);
?>
