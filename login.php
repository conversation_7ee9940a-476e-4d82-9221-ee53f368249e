<?php
session_start();

if (isset($_SESSION['user_id'])) {
    header('Location: main.php');
}
?>

<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat+Alternates&display=swap" rel="stylesheet">
    <meta name="theme-color" content="#0d0072"/>
    <link rel="manifest" href="/manifest.json" />
    <script src="https://cdn.tailwindcss.com"></script>
    <title></title>
    <style>
        * {
            font-family: 'Montserrat Alternates', sans-serif;
        }

        .bodi {
            min-height: 100vh;
            min-height: -webkit-fill-available;
            background-image: url('./assets/images/background.svg');
            background-repeat: no-repeat;
            background-size: cover;
        }

        html {
            height: -webkit-fill-available;
        }

        .up {
            animation-name: up;
            animation-duration: 1s;
        }

        @keyframes up {
            from {
                margin-bottom: -200%;
            }

            to {
                margin-bottom: 0;
            }
        }

        .down {
            animation-name: down;
            animation-duration: 1s;
        }

        @keyframes down {
            from {
                margin-bottom: 0;
            }

            to {
                margin-bottom: -200%;
            }
        }

        .errDown {
            animation-name: errdown;
            animation-duration: 0.4s;
        }

        @keyframes errdown {
            from {
                margin-top: -10%;
            }

            to {
                margin-top: 0;
            }
        }

        .errUp {
            animation-name: errup;
            animation-duration: 0.4s;
        }

        @keyframes errup {
            from {
                margin-top: 0;
            }

            to {
                margin-top: -20%;
            }
        }
        body {
            height: 100vh;
            overflow: auto;
        }
    </style>
</head>

<body class="bg-cover bg-no-repeat overflow-y-auto">
    <?php
if (isset($_GET['error']) && $_GET['error'] == 'akses') {
    ?>
    <div id="error-message" class="h-[10%] w-full errDown absolute flex items-center">
        <div class="flex bg-white h-max w-max p-1 items-center rounded-full m-auto">
            <div class="w-max m-auto flex items-center">
                <div class="bg-[#79be8c] rounded-full mr-1">
                    <img src="./assets/icons/exclamation.svg" alt="">
                </div>
                <div class="text-[#79be8c] ml-1 text-sm">
                    Akun anda bukan akun user
                </div>
            </div>
        </div>
    </div>
    <?php
}
?>
    <?php
if (isset($_GET['error']) && $_GET['error'] == 'salah') {
    ?>
    <div id="error-message" class="h-[10%] w-full errDown absolute flex items-center">
        <div class="flex bg-white h-max w-max p-1 items-center rounded-full m-auto">
            <div class="w-max m-auto flex items-center">
                <div class="bg-[#79be8c] rounded-full mr-1">
                    <img src="./assets/icons/exclamation.svg" alt="">
                </div>
                <div class="text-[#79be8c] ml-1 text-sm">
                    Email atau password salah
                </div>
            </div>
        </div>
    </div>
    <?php
}
?>
    <?php
if (isset($_GET['error']) && $_GET['error'] == 'nonaktif') {
    ?>
    <div id="error-message" class="h-[10%] w-full errDown absolute flex items-center">
        <div class="flex bg-white h-max w-max p-1 items-center rounded-full m-auto">
            <div class="w-max m-auto flex items-center">
                <div class="bg-red-500 rounded-full mr-1 p-1">
                    <img src="./assets/icons/exclamation.svg" alt="" class="w-4 h-4">
                </div>
                <div class="text-red-500 ml-1 text-sm">
                    Akun Anda tidak aktif. Hubungi administrator.
                </div>
            </div>
        </div>
    </div>
    <?php
}
?>
    <?php
if (isset($_SESSION['error'])) {
    ?>
    <div id="error-message" class="h-[10%] w-full errDown absolute flex items-center">
        <div class="flex bg-white h-max w-max p-1 items-center rounded-full m-auto">
            <div class="w-max m-auto flex items-center">
                <div class="bg-red-500 rounded-full mr-1 p-1">
                    <img src="./assets/icons/exclamation.svg" alt="" class="w-4 h-4">
                </div>
                <div class="text-red-500 ml-1 text-sm">
                    <?php echo $_SESSION['error']; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
    unset($_SESSION['error']);
}
?>
    <?php
if (isset($_SESSION['success'])) {
    ?>
    <div id="success-message" class="h-[10%] w-full errDown absolute flex items-center">
        <div class="flex bg-white h-max w-max p-1 items-center rounded-full m-auto">
            <div class="w-max m-auto flex items-center">
                <div class="bg-green-500 rounded-full mr-1 p-1">
                    <img src="./assets/icons/check.svg" alt="" class="w-4 h-4">
                </div>
                <div class="text-green-500 ml-1 text-sm">
                    <?php echo $_SESSION['success']; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
    unset($_SESSION['success']);
}
?>
    <div class="sm:block hidden">
        <div class="w-full h-full flex items-center">
            <div class=" m-auto w-14">
                <label class="text-lg font-bold">Mohon maaf website ini hanya bisa digunakan di
                    android</label>
                <img class="w-14 h-14 m-auto" src="./assets/icons/mobile-android.svg" alt="">
            </div>
        </div>
    </div>
    <div class="bodi sm:hidden">
        <div class="sm:hidden flex items-end h-full">
            <div class="mb-10 mx-auto"><label for="dafBtn"
                    class="font-bold block text-[18px] py-3 rounded-3xl text-[#8AE0A2] px-9 border-2 border-[#92E2A8]">Daftar</label>
                <input class="hidden" type="checkbox" name="dafBtn" id="dafBtn">
                <span class="block mt-2 text-center text-sm">Punya akun <label for="logBtn"
                        class="text-sm font-bold">Masuk</label></span>
                <input class="hidden" type="checkbox" name="logBtn" id="logBtn">
            </div>
            <div id="daftarBody" class="fixed z-10 opacity-0 h-full hidden w-full items-end bg-black">
            </div>
            <div id="daftarMasuk" class="fixed opacity-0 h-full hidden w-full items-end bg-black">
            </div>
            <div id="daftar" class="fixed min-h-max z-20 bg-white h-3/4 w-full rounded-t-3xl py-10 -mb-[200%]">
                <h1 class="font-bold text-3xl text-center text-[#8AE0A2] mb-3">Buat Akun</h1>
                <span class="block font-bold text-[#ABB1B3] text-center my-3">Buat akun anda</span>
                <form action="prosesDaftar.php" method="POST" class="h-5/6 text-center">
                    <div class="h-[17%] flex items-center">
                        <div class="px-5 rounded-2xl flex items-center w-3/4 mx-auto border border-gray-400 bg-white">
                            <label class="mr-2 w-[15%]" for="username"><img src="./assets/icons/users.svg"
                                    alt="User.svg"></label>
                            <input class="h-14 bg-transparent w-[85%] focus:outline-none text-sm border-none"
                                type="text" name="username" id="username" placeholder="Nama Pengguna" required
                                autocomplete="off">
                        </div>
                    </div>
                    <div class="h-[17%] flex items-center">
                        <div class="px-5 rounded-2xl flex items-center w-3/4 mx-auto border border-gray-400 bg-white">
                            <label class="mr-2 w-[15%]" for="name"><img src="./assets/icons/user-circle.svg"
                                    alt="User.svg"></label>
                            <input class="h-14 bg-transparent w-[85%] focus:outline-none text-sm border-none"
                                type="text" name="name" id="name" placeholder="Nama Lengkap" required
                                autocomplete="off">
                        </div>
                    </div>
                    <div class="h-[17%] flex items-center">
                        <div class="px-5 rounded-2xl flex items-center w-3/4 mx-auto border border-gray-400 bg-white">
                            <label class="mr-2 w-[15%]" for="phone"><img src="./assets/icons/phone.svg"
                                    alt="Phone.svg"></label>
                            <input class="h-14 bg-transparent w-[85%] focus:outline-none border-none text-sm"
                                type="number" name="phone" id="phone" placeholder="Nomor handphone" required
                                autocomplete="off">
                        </div>
                    </div>
                    <div class="h-[17%] flex items-center">
                        <div class="px-5 rounded-2xl flex items-center w-3/4 mx-auto border border-gray-400 bg-white">
                            <label class="mr-2 w-[15%]" for="email"><img src="./assets/icons/envelope.svg"
                                    alt="Envelope.svg"></label>
                            <input class="h-14 bg-transparent w-[85%] focus:outline-none border-none text-sm"
                                type="email" name="email" id="email" placeholder="Email" required autocomplete="off">
                        </div>
                    </div>
                    <div class="h-[17%] flex items-center">
                        <div class="px-5 rounded-2xl flex items-center w-3/4 mx-auto border border-gray-400 bg-white">
                            <label class="mr-2 w-[15%]" for="passwordDaftar">
                                <img src="./assets/icons/lock-alt.svg" alt="lock.svg">
                            </label>
                            <input class="bg-transparent h-14 w-[75%] focus:outline-none border-none text-sm" 
                                type="password" name="password" id="passwordDaftar" required placeholder="Password" autocomplete="off">
                            
                            <button type="button" id="buttonMata" class="w-[10%]">
                                <img id="ikonMata" src="./assets/icons/eye.svg" alt="Show password">
                            </button>
                        </div>
                    </div>
                    <input type="text" hidden name="level" id="level" value="user">
                    <div class="h-[17%] flex items-center">
                        <div class="m-auto w-max">
                            <input
                                class="font-bold block text-[18px] py-3 rounded-3xl text-[#8AE0A2] px-9 border-2 border-[#92E2A8]"
                                type="submit" name="submit" id="submit" value="Daftar">
                        </div>
                    </div>
                </form>
            </div>
            <div id="toast" class="hidden fixed top-5 right-5 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg">
                <p id="toast-message"></p>
            </div>
            <div id="masuk" class="fixed z-10 bg-white h-3/4 w-full rounded-t-3xl py-10 -mb-[200%]">
                <h1 class="font-bold text-3xl text-center text-[#8AE0A2] mb-3">Selamat Datang</h1><span
                    class="block font-bold text-[#ABB1B3] text-center my-3">Masuk ke akun anda</span>
                <form action="prosesLogin.php" method="POST" class="h-3/4 text-center">
                    <div class="h-1/5 flex items-center">
                        <div class="px-5 rounded-2xl flex items-center w-3/4 mx-auto border border-gray-400 bg-white">
                            <label class="mr-2 w-[15%]" for="name"><img src="./assets/icons/envelope.svg"
                                    alt="User.svg"></label>
                            <input class="h-14 bg-transparent w-[85%] focus:outline-none border-none text-lg"
                                type="email" name="name" id="name" value="<?php echo $_COOKIE['email'] ?? ''; ?>" placeholder="Email" required
                                autocomplete="off"></div>
                    </div>
                    <div class="h-1/5 flex items-center">
                        <div class="px-5 rounded-2xl flex items-center w-3/4 mx-auto border border-gray-400 bg-white">
                            <label class="mr-2 w-[15%]" for="name">
                                <img src="./assets/icons/lock-alt.svg" alt="lock.svg">
                            </label>
                            <input class="bg-transparent block h-14 w-[70%] focus:outline-none border-none text-lg" type="password"
                                name="password" id="password-masuk" required placeholder="Password" autocomplete="off">
                            <button type="button" id="togglePassword" class="w-[15%]">
                                <img id="eyeIcon" width="30px" height="30px" src="./assets/icons/eye.svg" alt="Show password">
                            </button>
                        </div>
                    </div>
                    <div class="h-1/6 w-4/5 flex items-center mx-auto">
                        <div class="flex items-center w-full">
                            <div class="w-1/2 text-start">
                            <input type="checkbox" name="remember" id="remember" 
                                <?php echo isset($_COOKIE['email']) ? 'checked' : ''; ?>>
                                <label for="remember">Ingat Saya</label>
                            </div>
                            <div class="w-1/2 text-end">
                                <a href="lupa_password.php" class="hover:text-[#8AE0A2]">Lupa Password?</a>
                            </div>
                        </div>
                    </div>
                    <div class="h-3/5 flex items-center">
                        <div class="m-auto w-max"><input
                                class="font-bold block text-[18px] py-3 rounded-3xl text-[#8AE0A2] px-9 border-2 border-[#92E2A8]"
                                type="submit" name="submit" id="submit" value="Masuk"></div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>

<script>
    window.addEventListener('resize', function () {
        var bodyHeight = document.body.offsetHeight;
        window.innerHeight = bodyHeight;
    });

    const checkDaf = document.querySelector("#dafBtn");
    const daftar = document.querySelector("#daftar");
    const checkLog = document.querySelector("#logBtn")
    const masuk = document.querySelector("#masuk")
    const dafBody = document.querySelector("#daftarBody");
    const masukBody = document.querySelector("#daftarMasuk");
    const togglePassword = document.querySelector("#togglePassword");
    const passwordInput = document.querySelector("#password-masuk");
    const eyeIcon = document.querySelector("#eyeIcon");
    const passwordDaftar = document.getElementById("passwordDaftar");
    const buttonMata = document.getElementById("buttonMata");
    const ikonMata = document.getElementById("ikonMata");
    
    buttonMata.addEventListener("click", function (){
       const tipeBenar = passwordDaftar.getAttribute("type");
       const tipeBaru = tipeBenar === "password" ? "text" : "password";
       passwordDaftar.setAttribute("type", tipeBaru);
       
       console.log("tipe saat ini:", tipeBaru);
       
       ikonMata.src = tipeBaru === "password" ? "./assets/icons/eye.svg" : "./assets/icons/eye-slash.svg";
    });

    togglePassword.addEventListener("click", function () {
        // Toggle the type attribute between password and text
        const currentType = passwordInput.getAttribute("type");
        const newType = currentType === "password" ? "text" : "password";
        passwordInput.setAttribute("type", newType);

        // Debugging log to check if type changes
        console.log('Current input type:', newType);

        // Toggle the eye icon
        eyeIcon.src = newType === "password" ? "./assets/icons/eye.svg" : "./assets/icons/eye-slash.svg";
    });

    document.getElementById('phone').addEventListener('input', function (event) {
        let value = event.target.value;
        // Remove non-digit characters
        value = value.replace(/\D/g, '');
        // Check if the number starts with '0' and replace it with '62'
        if (value.startsWith('0')) {
            value = '62' + value.substring(1);
        }
        // Update the input field with the modified value
        event.target.value = value;
    });


    // Function untuk menampilkan halaman daftar
    function showDaftar() {
        daftar.classList.remove("-mb-[200%]");
        masukBody.classList.add("opacity-30");
        masukBody.classList.remove("opacity-0");
        masukBody.classList.remove("hidden");
        daftar.classList.add("up");
        daftar.classList.remove("down"); // Hapus kelas "down" ketika checkbox diubah
        daftar.classList.add("py-10");
    }

    // Function untuk menampilkan halaman masuk
    function showMasuk() {
        masuk.classList.remove("-mb-[200%]");
        dafBody.classList.add("opacity-30");
        dafBody.classList.remove("opacity-0");
        dafBody.classList.remove("hidden");
        masuk.classList.add("up");
        masuk.classList.remove("down"); // Hapus kelas "down" ketika checkbox diubah
        masuk.classList.add("py-10");
    }

    if (checkDaf && daftar) {
        checkDaf.addEventListener('change', () => {
            showDaftar();
            history.pushState({
                isChecked: checkDaf.checked
            }, ""); // Tambahkan state ke dalam history
        });

        masukBody.addEventListener('click', () => {
            checkDaf.checked = !checkDaf.checked;
            if (checkDaf.checked) {
                showDaftar();
            } else {
                daftar.classList.add("-mb-[200%]", "up");
                daftar.classList.add("down");
                masukBody.classList.remove("opacity-30");
                masukBody.classList.add("opacity-0", "hidden");
            }
            history.pushState({
                isChecked: checkDaf.checked
            }, ""); // Tambahkan state ke dalam history
        });
    }

    if (checkLog && masuk) {
        checkLog.addEventListener('change', () => {
            showMasuk();
            history.pushState({
                isChecked: checkLog.checked
            }, ""); // Tambahkan state ke dalam history
        });

        dafBody.addEventListener('click', () => {
            checkLog.checked = !checkLog.checked;
            if (checkLog.checked) {
                showMasuk();
            } else {
                masuk.classList.add("-mb-[200%]", "up");
                masuk.classList.add("down");
                dafBody.classList.remove("opacity-30");
                dafBody.classList.add("opacity-0", "hidden");
                setTimeout(() => {
                    masuk.classList.remove("py-10");
                }, 1000);
            }
            history.pushState({
                isChecked: checkLog.checked
            }, ""); // Tambahkan state ke dalam history
        });
    }

    // Event popstate untuk menangani ketika tombol back di browser ditekan
    window.addEventListener('popstate', (event) => {
        // Jika terdapat state di dalam history
        if (event.state) {
            // Jika checkbox daftar di-check
            if (event.state.isChecked && checkDaf) {
                checkDaf.checked = true;
                showDaftar();
            } else if (checkDaf) {
                checkDaf.checked = false;
                daftar.classList.add("-mb-[200%]", "up");
                daftar.classList.add("down");
                masukBody.classList.remove("opacity-30");
                masukBody.classList.add("opacity-0", "hidden");
                setTimeout(() => {
                    daftar.classList.remove("py-10");
                }, 1000);
            }
            // Jika checkbox masuk di-check
            if (event.state.isChecked && checkLog) {
                checkLog.checked = true;
                showMasuk();
            } else if (checkLog) {
                checkLog.checked = false;
                masuk.classList.add("-mb-[200%]", "up");
                masuk.classList.add("down");
                dafBody.classList.remove("opacity-30");
                dafBody.classList.add("opacity-0", "hidden");
                setTimeout(() => {
                    masuk.classList.remove("py-10");
                }, 1000);
            }
        } else {
            // Jika tidak terdapat state di dalam history
            if (checkDaf) {
                checkDaf.checked = false;
                daftar.classList.add("-mb-[200%]", "up");
                daftar.classList.add("down");
                masukBody.classList.remove("opacity-30");
                masukBody.classList.add("opacity-0", "hidden");
                setTimeout(() => {
                    daftar.classList.remove("py-10");
                }, 1000);
            }
            if (checkLog) {
                checkLog.checked = false;
                masuk.classList.add("-mb-[200%]", "up");
                masuk.classList.add("down");
                dafBody.classList.remove("opacity-30");
                dafBody.classList.add("opacity-0", "hidden");
                setTimeout(() => {
                    masuk.classList.remove("py-10");
                }, 1000);
            }
        }
    });

    // Handle error message
    const errorMessage = document.getElementById('error-message');
    if (errorMessage) {
        setTimeout(function () {
            errorMessage.classList.remove('errDown');
            errorMessage.classList.add('errUp');
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 300);
        }, 3000);
    }

    // Handle success message
    const successMessage = document.getElementById('success-message');
    if (successMessage) {
        setTimeout(function () {
            successMessage.classList.remove('errDown');
            successMessage.classList.add('errUp');
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 300);
        }, 3000);
    }
</script>

</html>