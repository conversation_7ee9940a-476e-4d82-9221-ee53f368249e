<?php
// Ambil data dari request
$input_data = file_get_contents("php://input");
$data = json_decode($input_data);

// Pastikan ada ID dan koordinat yang dikirim
if (isset($data->id) && isset($data->coordinates)) {
    // Simpan ID polyline dan koordinat baru
    $polyline_id = $data->id;
    $new_coordinates = json_encode($data->coordinates); // Ubah kembali ke format JSON untuk disimpan

    // Implementasikan kode untuk memperbarui polyline di database
    // Gantilah dengan koneksi dan logika database sesuai dengan kebutuhan Anda

    // Contoh koneksi ke database MySQL
    $servername = "localhost";
    $username = "root";
    $password = "";
    $dbname = "db_sampah";

    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);

    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    // Buat query untuk memperbarui koordinat polyline berdasarkan ID
    $sql = "UPDATE area SET coordinates = '$new_coordinates' WHERE id = $polyline_id";

    if ($conn->query($sql) === TRUE) {
        $response = [
            'status' => 'success',
            'message' => 'Polyline updated successfully'
        ];
        echo json_encode($response);
    } else {
        $response = [
            'status' => 'error',
            'message' => 'Error updating polyline: ' . $conn->error
        ];
        echo json_encode($response);
    }

    $conn->close();
} else {
    $response = [
        'status' => 'error',
        'message' => 'ID or coordinates not provided'
    ];
    echo json_encode($response);
}
?>
