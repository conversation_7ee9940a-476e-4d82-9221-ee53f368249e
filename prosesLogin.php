<?php
include 'config.php';

// Memeriksa apakah tombol Submit ditekan
if (isset($_POST['submit'])) {
    // Ambil input username dan password dari form
    $username = $_POST['name'];
    $password = md5($_POST['password']);
    $remember = isset($_POST['remember']);

    // Validasi input untuk mencegah SQL injection
    $username = mysqli_real_escape_string($conn, $username);

    // Cek apakah username dan password sesuai dengan data yang tersimpan di database
    // Tambahkan validasi status = 1 (aktif) - hanya user aktif yang bisa login
    $query = "SELECT * FROM users WHERE email=? AND password=? AND status=1 AND level='user'";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ss", $username, $password);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    if (mysqli_num_rows($result) == 1) {
        // Jika sesuai, simpan data pengguna ke session
        $row = mysqli_fetch_assoc($result);

        // set session variables
        session_start();
        $_SESSION['user_id'] = filter_var($row['user_id'], FILTER_SANITIZE_NUMBER_INT);

        if ($remember) {
            setcookie('email', $username, time() + (86400 * 30), "/"); // 86400 = 1 day
        } else {
            if (isset($_COOKIE['email'])) {
                setcookie('email', '', time() - 3600, "/"); // Hapus cookie
            }
        }

        // Redirect ke halaman utama
        header("Location: main.php");
        exit();
    } else {
        // Cek apakah email ada tapi password salah atau akun tidak aktif
        $check_query = "SELECT * FROM users WHERE email=? AND level='user'";
        $check_stmt = mysqli_prepare($conn, $check_query);
        mysqli_stmt_bind_param($check_stmt, "s", $username);
        mysqli_stmt_execute($check_stmt);
        $check_result = mysqli_stmt_get_result($check_stmt);

        if (mysqli_num_rows($check_result) == 1) {
            $check_row = mysqli_fetch_assoc($check_result);
            if ($check_row['password'] != $password) {
                // Password salah
                header("Location: login.php?error=salah");
            } else if ($check_row['status'] == 0) {
                // Akun tidak aktif
                header("Location: login.php?error=nonaktif");
            } else {
                // Error lainnya
                header("Location: login.php?error=salah");
            }
        } else {
            // Email tidak ditemukan
            header("Location: login.php?error=salah");
        }
        exit();
    }
}

// Menutup koneksi
mysqli_close($conn);
?>
