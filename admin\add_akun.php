<?php
if(isset($_POST['submit'])){
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "db_sampah";

$conn = mysqli_connect($servername, $username, $password, $dbname);

if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

$name = $_POST['name'];
$phone = $_POST['phone'];
$email = $_POST['email'];
$password = md5($_POST['password']);
$level = $_POST['level'];

$name = mysqli_real_escape_string($conn, $name);
$phone = mysqli_real_escape_string($conn, $phone);
$email = mysqli_real_escape_string($conn, $email);
$password = mysqli_real_escape_string($conn, $password);
$level = mysqli_real_escape_string($conn, $level);

// Default image
$default_image = "./assets/imagesAcc/default.png";


$sql = "INSERT INTO users (full_name, email, no_hp, password, level, image)
VALUES ('$name', '$email', '$phone', '$password', '$level', '$default_image')";

if (mysqli_query($conn, $sql)) {
    echo "New record created successfully";
    header('Location: add_kendaraan.php');
} else {
    echo "Error: " . $sql . "<br>" . mysqli_error($conn);
}

mysqli_close($conn);
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Add Akun Modal</title>
    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 50%;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h2>Add Akun</h2>
    <button onclick="openModal()">Open Modal</button>

    <div id="addAkunModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>Add Akun</h3>
            <form method="POST" action="add_akun.php">
                <label for="name">Full Name:</label>
                <input type="text" name="name" required><br><br>
                <label for="phone">Phone:</label>
                <input type="text" name="phone" required><br><br>
                <label for="email">Email:</label>
                <input type="email" name="email" required><br><br>
                <label for="password">Password:</label>
                <input type="password" name="password" required><br><br>
                <input type="hidden" name="level" value="driver">
                <input type="submit" value="Submit">
            </form>
        </div>
    </div>

    <script>
        // Get the modal element
        var modal = document.getElementById("addAkunModal");

        // Function to open the modal
        function openModal() {
            modal.style.display = "block";
        }

        // Function to close the modal
        function closeModal() {
            modal.style.display = "none";
        }

        // Close the modal when the user clicks outside of it
        window.onclick = function(event) {
            if (event.target == modal) {
                closeModal();
            }
        };
    </script>
</body>
</html>


