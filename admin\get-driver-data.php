<?php
// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'db_sampah');

session_start();

// Get the latitude and longitude of the reported trash location
$reportedLatitude = $_SESSION["longitude"]; // Latitude of the reported trash location
$reportedLongitude = $_SESSION["latitude"]; // Longitude of the reported trash location

// Fetch driver data from the database within 2 km radius
$sql = "SELECT d.driver_id, u.alamat, u.latitude, u.longitude, u.full_name, (6371 * ACOS(COS(RADIANS($reportedLatitude)) * COS(RADIANS(u.latitude)) * COS(RADIANS(u.longitude) - RADIANS($reportedLongitude)) + SIN(RADIANS($reportedLatitude)) * SIN(RADIANS(u.latitude)))) AS distance
        FROM users u
        JOIN driver d ON d.user_id = u.user_id
        HAVING distance <= 2
        ORDER BY distance ASC";

$result = mysqli_query($conn, $sql);

// Prepare the response data
$response = array();
while ($row = mysqli_fetch_assoc($result)) {
    $response[] = $row;
}

// Return the response as JSON
header('Content-Type: application/json');
echo json_encode($response);
?>
